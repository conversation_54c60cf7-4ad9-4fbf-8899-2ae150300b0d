package com.example.castapp.model

import android.content.Context
import android.graphics.RectF
import androidx.core.content.edit
import com.example.castapp.utils.AppLog
import com.google.gson.Gson
import java.util.concurrent.ConcurrentHashMap

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
data class RemoteWindowConfig(
    // 基础信息
    val connectionId: String,
    val ipAddress: String,
    val port: Int,
    val isActive: Boolean,
    val deviceName: String?,
    val note: String?,

    // 🎯 新增：文字窗口内容数据（集中管理）
    var textContent: String = "",
    var richTextData: String? = null,
    var isBold: Boolean = false,
    var isItalic: Boolean = false,
    var fontSize: Int = 13,
    var fontName: String? = null,
    var fontFamily: String? = null,
    var lineSpacing: Float = 0.0f,
    var textAlignment: Int = android.view.Gravity.CENTER,
    var isWindowTextColorEnabled: Boolean = false,
    var windowTextBackgroundColor: Int = 0xFFFFFFFF.toInt(),

    // 位置和变换参数（实时更新）
    var positionX: Float,
    var positionY: Float,
    var scaleFactor: Float,
    var rotationAngle: Float,
    val zOrder: Int,

    // 功能开关
    val isCropping: Boolean,
    val isDragEnabled: Boolean,
    val isScaleEnabled: Boolean,
    val isRotationEnabled: Boolean,
    var isVisible: Boolean,
    var isMirrored: Boolean,

    // 样式参数（实时更新）
    var cornerRadius: Float,
    var alpha: Float,
    var isBorderEnabled: Boolean,
    var borderColor: Int,
    var borderWidth: Float,

    // 控制参数
    val isControlEnabled: Boolean,
    val isEditEnabled: Boolean,

    // 窗口尺寸
    val baseWindowWidth: Int,
    val baseWindowHeight: Int,

    // 背景和模式
    val windowColorEnabled: Boolean,
    val windowBackgroundColor: Int,
    val isLandscapeModeEnabled: Boolean,

    // 裁剪参数
    val cropRectRatio: RectF? = null,

    // 🎯 新增：数据来源和时间戳
    val dataSource: String = "unknown", // 数据来源：receiver/cache/visualization
    val lastUpdated: Long = System.currentTimeMillis() // 最后更新时间
) {
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    companion object {
        fun fromCastWindowInfo(windowInfo: CastWindowInfo, context: android.content.Context? = null): RemoteWindowConfig {
            // 🎯 关键修复：对于文字窗口，从SharedPreferences中读取保存的文字内容和格式
            var textContent = ""
            var richTextData: String? = null
            var isBold = false
            var isItalic = false
            var fontSize = 13
            var fontName: String? = null
            var fontFamily: String? = null
            var lineSpacing = 0.0f
            var textAlignment = android.view.Gravity.CENTER
            var isWindowTextColorEnabled = false
            var windowTextBackgroundColor = 0xFFFFFFFF.toInt()

            val isTextWindow = windowInfo.connectionId.startsWith("text_")
            if (isTextWindow && context != null) {
                try {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(context)

                    // 优先尝试获取富文本格式
                    val richTextSpannable = textFormatManager.getRichTextFormat(windowInfo.connectionId)
                    if (richTextSpannable != null) {
                        textContent = richTextSpannable.toString()
                        richTextData = textFormatManager.serializeSpannableString(richTextSpannable)
                        AppLog.d("【统一配置管理器】📝 已从SharedPreferences恢复富文本格式: ${windowInfo.connectionId}, 内容='$textContent'")
                    } else {
                        // 后备方案：获取基本格式
                        val formatInfo = textFormatManager.getTextFormat(windowInfo.connectionId)
                        if (formatInfo != null) {
                            textContent = formatInfo.textContent
                            isBold = formatInfo.isBold
                            isItalic = formatInfo.isItalic
                            fontSize = formatInfo.fontSize
                            AppLog.d("【统一配置管理器】📝 已从SharedPreferences恢复基本格式: ${windowInfo.connectionId}, 内容='$textContent'")
                        } else {
                            AppLog.d("【统一配置管理器】📝 未找到保存的文字格式: ${windowInfo.connectionId}")
                        }
                    }

                    // 尝试获取扩展格式信息
                    val extendedFormat = textFormatManager.getExtendedTextFormat(windowInfo.connectionId)
                    if (extendedFormat != null) {
                        fontName = extendedFormat.fontName
                        fontFamily = extendedFormat.fontFamily
                        lineSpacing = extendedFormat.lineSpacing
                        textAlignment = extendedFormat.textAlignment
                        AppLog.d("【统一配置管理器】📝 已恢复扩展格式信息: 字体=$fontName, 行间距=${lineSpacing}dp, 对齐=$textAlignment")
                    }
                } catch (e: Exception) {
                    AppLog.e("【统一配置管理器】📝 恢复文字格式失败: ${windowInfo.connectionId}", e)
                }
            }

            return RemoteWindowConfig(
                connectionId = windowInfo.connectionId,
                ipAddress = windowInfo.ipAddress,
                port = windowInfo.port,
                isActive = windowInfo.isActive,
                deviceName = windowInfo.deviceName,
                note = windowInfo.note,
                // 🎯 添加文字内容和格式信息
                textContent = textContent,
                richTextData = richTextData,
                isBold = isBold,
                isItalic = isItalic,
                fontSize = fontSize,
                fontName = fontName,
                fontFamily = fontFamily,
                lineSpacing = lineSpacing,
                textAlignment = textAlignment,
                isWindowTextColorEnabled = isWindowTextColorEnabled,
                windowTextBackgroundColor = windowTextBackgroundColor,
                positionX = windowInfo.positionX,
                positionY = windowInfo.positionY,
                scaleFactor = windowInfo.scaleFactor,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                isCropping = windowInfo.isCropping,
                isDragEnabled = windowInfo.isDragEnabled,
                isScaleEnabled = windowInfo.isScaleEnabled,
                isRotationEnabled = windowInfo.isRotationEnabled,
                isVisible = windowInfo.isVisible,
                isMirrored = windowInfo.isMirrored,
                cornerRadius = windowInfo.cornerRadius,
                alpha = windowInfo.alpha,
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                isControlEnabled = windowInfo.isControlEnabled,
                isEditEnabled = windowInfo.isEditEnabled,
                baseWindowWidth = windowInfo.baseWindowWidth,
                baseWindowHeight = windowInfo.baseWindowHeight,
                windowColorEnabled = windowInfo.windowColorEnabled,
                windowBackgroundColor = windowInfo.windowBackgroundColor,
                isLandscapeModeEnabled = windowInfo.isLandscapeModeEnabled,
                cropRectRatio = windowInfo.cropRectRatio
            )
        }
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    fun toBatchSyncData(): Map<String, Any> {
        val data = mutableMapOf<String, Any>(
            "connectionId" to connectionId,
            "ipAddress" to ipAddress,
            "port" to port,
            "isActive" to isActive,
            "deviceName" to (deviceName ?: "未知设备"),
            "note" to (note ?: "无"),
            "positionX" to positionX,
            "positionY" to positionY,
            "scaleFactor" to scaleFactor,
            "rotationAngle" to rotationAngle,
            "zOrder" to zOrder,
            "isCropping" to isCropping,
            "isDragEnabled" to isDragEnabled,
            "isScaleEnabled" to isScaleEnabled,
            "isRotationEnabled" to isRotationEnabled,
            "isVisible" to isVisible,
            "isMirrored" to isMirrored,
            "cornerRadius" to cornerRadius,
            "alpha" to alpha,
            "isControlEnabled" to isControlEnabled,
            "isEditEnabled" to isEditEnabled,
            "isBorderEnabled" to isBorderEnabled,
            "borderColor" to borderColor,
            "borderWidth" to borderWidth,
            "baseWindowWidth" to baseWindowWidth,
            "baseWindowHeight" to baseWindowHeight,
            "windowColorEnabled" to windowColorEnabled,
            "windowBackgroundColor" to windowBackgroundColor,
            "isLandscapeModeEnabled" to isLandscapeModeEnabled
        )

        // 🎯 关键修复：添加文字窗口相关字段
        if (connectionId.startsWith("text_")) {
            data["textContent"] = textContent ?: ""
            data["richTextData"] = richTextData ?: ""
            data["isBold"] = isBold
            data["isItalic"] = isItalic
            data["fontSize"] = fontSize
            data["fontName"] = fontName ?: ""
            data["fontFamily"] = fontFamily ?: ""
            data["lineSpacing"] = lineSpacing
            data["textAlignment"] = textAlignment
            data["isWindowTextColorEnabled"] = isWindowTextColorEnabled
            data["windowTextBackgroundColor"] = windowTextBackgroundColor
            AppLog.d("【统一配置管理器】📝 批量同步数据包含文字内容: $connectionId, 内容='$textContent'")
        }

        // 添加裁剪区域信息（如果存在）
        cropRectRatio?.let { cropRatio ->
            data["cropRectRatio"] = mapOf(
                "left" to cropRatio.left,
                "top" to cropRatio.top,
                "right" to cropRatio.right,
                "bottom" to cropRatio.bottom
            )
        }

        return data
    }
}

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
class RemoteWindowConfigManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteWindowConfigManager? = null

        fun getInstance(): RemoteWindowConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteWindowConfigManager().also { INSTANCE = it }
            }
        }

        private const val PREFS_NAME = "remote_window_config"
        private const val KEY_PREFIX = "config_"
    }

    // 🎯 每个接收端的窗口配置映射：receiverId -> (connectionId -> RemoteWindowConfig)
    private val receiverConfigs = ConcurrentHashMap<String, ConcurrentHashMap<String, RemoteWindowConfig>>()

    private val gson = Gson()

    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    fun updateFromReceiverData(receiverId: String, windowInfoList: List<CastWindowInfo>, context: android.content.Context? = null) {
        AppLog.d("【统一配置管理器】从接收端更新窗口配置: $receiverId, ${windowInfoList.size} 个窗口")

        val receiverConfigMap = receiverConfigs.getOrPut(receiverId) { ConcurrentHashMap() }

        // 清除旧配置
        receiverConfigMap.clear()

        // 添加新配置
        windowInfoList.forEach { windowInfo ->
            val config = RemoteWindowConfig.fromCastWindowInfo(windowInfo, context).copy(
                dataSource = "receiver",
                lastUpdated = System.currentTimeMillis()
            )
            receiverConfigMap[windowInfo.connectionId] = config
            AppLog.d("【统一配置管理器】更新窗口配置: ${windowInfo.connectionId}")
        }

        AppLog.d("【统一配置管理器】接收端数据更新完成: $receiverId")
    }

    /**
     * 🎯 从可视化组件同步实时参数
     */
    fun syncVisualizationParams(receiverId: String, visualizationDataList: List<WindowVisualizationData>) {
        AppLog.d("【统一配置管理器】同步可视化参数: $receiverId, ${visualizationDataList.size} 个窗口")

        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        visualizationDataList.forEach { visualData ->
            receiverConfigMap[visualData.connectionId]?.let { config ->
                // 🎯 关键修复：使用可视化界面的实际位置，而不是原始位置
                // 需要将可视化位置转换回接收端坐标系
                val actualPositionX = visualData.visualizedX / visualData.remoteControlScale.toFloat()
                val actualPositionY = visualData.visualizedY / visualData.remoteControlScale.toFloat()

                // 🎯 关键修复：如果窗口尺寸发生变化，需要创建新的配置对象
                val needsNewConfig = (config.baseWindowWidth != visualData.originalWidth ||
                                    config.baseWindowHeight != visualData.originalHeight)

                if (needsNewConfig) {
                    // 创建新的配置对象，包含更新的尺寸
                    val updatedConfig = config.copy(
                        positionX = actualPositionX,
                        positionY = actualPositionY,
                        scaleFactor = visualData.scaleFactor,
                        rotationAngle = visualData.rotationAngle,
                        alpha = visualData.alpha,
                        isVisible = visualData.isVisible,
                        isMirrored = visualData.isMirrored,
                        cornerRadius = visualData.cornerRadius,
                        isBorderEnabled = visualData.isBorderEnabled,
                        borderColor = visualData.borderColor,
                        borderWidth = visualData.borderWidth,
                        baseWindowWidth = visualData.originalWidth,
                        baseWindowHeight = visualData.originalHeight,
                        lastUpdated = System.currentTimeMillis()
                    )
                    receiverConfigMap[visualData.connectionId] = updatedConfig
                    AppLog.d("【统一配置管理器】🎯 窗口尺寸已更新: ${visualData.connectionId} -> ${visualData.originalWidth}x${visualData.originalHeight}")
                } else {
                    // 只更新实时参数
                    config.positionX = actualPositionX
                    config.positionY = actualPositionY
                    config.scaleFactor = visualData.scaleFactor
                    config.rotationAngle = visualData.rotationAngle
                    config.alpha = visualData.alpha
                    config.isVisible = visualData.isVisible
                    config.isMirrored = visualData.isMirrored
                    config.cornerRadius = visualData.cornerRadius
                    config.isBorderEnabled = visualData.isBorderEnabled
                    config.borderColor = visualData.borderColor
                    config.borderWidth = visualData.borderWidth
                }

                // 🎯 关键修复：对于文字窗口，同步文字内容和格式信息
                if (visualData.connectionId.startsWith("text_")) {
                    // 获取当前配置（可能是新创建的配置对象）
                    val currentConfig = receiverConfigMap[visualData.connectionId] ?: config

                    currentConfig.textContent = visualData.textContent
                    currentConfig.richTextData = visualData.richTextData
                    currentConfig.isBold = visualData.isBold
                    currentConfig.isItalic = visualData.isItalic
                    currentConfig.fontSize = visualData.fontSize
                    currentConfig.fontName = visualData.fontName
                    currentConfig.fontFamily = visualData.fontFamily
                    currentConfig.lineSpacing = visualData.lineSpacing
                    currentConfig.textAlignment = visualData.textAlignment
                    currentConfig.isWindowTextColorEnabled = visualData.isWindowColorEnabled
                    currentConfig.windowTextBackgroundColor = visualData.windowBackgroundColor

                    AppLog.d("【统一配置管理器】📝 同步文字内容: ${visualData.connectionId}, 内容='${visualData.textContent}'")
                }

                AppLog.d("【统一配置管理器】同步可视化参数: ${visualData.connectionId}")
                AppLog.d("  🎯 位置转换: 可视化位置(${visualData.visualizedX}, ${visualData.visualizedY}) -> 接收端位置($actualPositionX, $actualPositionY)")
                AppLog.d("  缩放: ${visualData.scaleFactor}, 旋转: ${visualData.rotationAngle}°")
            }
        }

    }

    /**
     * 🎯 更新单个窗口参数
     */
    fun updateWindowConfig(receiverId: String, connectionId: String, updateAction: (RemoteWindowConfig) -> RemoteWindowConfig) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { currentConfig ->
            val updatedConfig = updateAction(currentConfig).copy(lastUpdated = System.currentTimeMillis())
            receiverConfigMap[connectionId] = updatedConfig

            AppLog.d("【统一配置管理器】更新窗口参数: $receiverId/$connectionId")
        }
    }

    /**
     * 🎯 获取批量同步数据
     */
    fun getBatchSyncData(receiverId: String): List<Map<String, Any>> {
        return receiverConfigs[receiverId]?.values?.map { it.toBatchSyncData() } ?: emptyList()
    }

    /**
     * 🎯 新增：更新文字窗口内容
     */
    fun updateTextContent(receiverId: String, connectionId: String, textContent: String, richTextData: String? = null) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { config ->
            config.textContent = textContent
            config.richTextData = richTextData
            AppLog.d("【统一配置管理器】📝 更新文字内容: $receiverId/$connectionId, 内容='$textContent'")
        }
    }

    /**
     * 🎯 新增：更新文字窗口格式
     */
    fun updateTextFormat(
        receiverId: String,
        connectionId: String,
        isBold: Boolean? = null,
        isItalic: Boolean? = null,
        fontSize: Int? = null,
        fontName: String? = null,
        fontFamily: String? = null,
        lineSpacing: Float? = null,
        textAlignment: Int? = null
    ) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { config ->
            isBold?.let { config.isBold = it }
            isItalic?.let { config.isItalic = it }
            fontSize?.let { config.fontSize = it }
            fontName?.let { config.fontName = it }
            fontFamily?.let { config.fontFamily = it }
            lineSpacing?.let { config.lineSpacing = it }
            textAlignment?.let { config.textAlignment = it }
            AppLog.d("【统一配置管理器】📝 更新文字格式: $receiverId/$connectionId")
        }
    }

    /**
     * 🎯 新增：获取文字窗口配置
     */
    fun getTextWindowConfig(receiverId: String, connectionId: String): RemoteWindowConfig? {
        return receiverConfigs[receiverId]?.get(connectionId)?.takeIf {
            connectionId.startsWith("text_")
        }
    }

    /**
     * 🎯 保存配置到本地存储
     */
    fun saveToStorage(context: Context, receiverId: String) {
        try {
            val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val configMap = receiverConfigs[receiverId] ?: return

            val jsonString = gson.toJson(configMap.values.toList())
            sharedPrefs.edit {
                putString("$KEY_PREFIX$receiverId", jsonString)
                putLong("${KEY_PREFIX}${receiverId}_timestamp", System.currentTimeMillis())
            }

            AppLog.d("【统一配置管理器】已保存配置到本地存储: $receiverId")
        } catch (e: Exception) {
            AppLog.e("【统一配置管理器】保存配置失败: $receiverId", e)
        }
    }
}
